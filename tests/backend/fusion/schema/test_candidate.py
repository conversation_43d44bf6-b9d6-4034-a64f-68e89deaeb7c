from backend.fusion.schema.attribute import Attribute
from backend.fusion.schema.entity import Entity
from backend.fusion.schema.entity_triple import EntityTriples
from backend.fusion.schema.relation import Relation


class TestCandidate:
    def test_candidate(self):
        candidate = EntityTriples(head=Entity(eid='eid', name='name', type='其他'),
                              relations=[Relation(relation='relation', tail=Entity(eid='e_id2', name='name2', type='其他'), type='base')],
                              attributes=[Attribute(attribute_name='attribute', attribute_value='value')])
        assert candidate.head.eid == 'eid'
        assert candidate.head.name == 'name'
        assert candidate.relations[0].relation == 'relation'
        assert candidate.relations[0].tail.eid == 'e_id2'
        assert candidate.relations[0].tail.name == 'name2'
        assert candidate.attributes[0].attribute_name == 'attribute'
        assert candidate.attributes[0].attribute_value == 'value'
