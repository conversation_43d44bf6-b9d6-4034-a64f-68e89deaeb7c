from backend.fusion import AutoAdd
from docarray import Document, DocumentArray
from jina import Flow


def test_auto_add():
    f = Flow().add(uses=AutoAdd)
    with f:
        docs = DocumentArray()
        docs.append(Document(tags={"text": "我叫汤姆去拿外衣", "pos": "pos"}))
        predict_res = f.post('/auto_add', docs)
        assert predict_res[0].tags == {"text": "我叫汤姆去拿外衣", "pos": "pos"}
        docs = DocumentArray()
        docs.append(Document(tags={"text": "hello"}))
        foo_res = f.post('/auto_add', docs)
        assert foo_res[0].tags == {"text": "hello"}
