es:
  add: yftool-db-elasticsearch:9200
  entity_index: entity_kg_v3

candidate_generator:
  candidate_count: 5
  delete_attributes:
    [
      'create_time',
      'update_time',
      '_show_name',
      '_eid',
      'name',
      '_create_time',
      '_update_time',
    ]

neo4j: # neo4j
  schema: 'bolt' # neo4j 或 bolt
  host: 'yftool-db-neo4j'
  port: 7687
  username: 'neo4j'
  password: 'yunfu2017'
  limit: 3

fast_sim:
  idf_file: /opt/yunfu/yfproduct/yfkm/services/kg/fusion/data/idf.txt
  threshold: 0.5

alias_file: tests/fixtures/entity_alias.tsv
