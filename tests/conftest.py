import pytest
from yunfu.common import ConfigUtils

from backend.fusion.candidate_generator import CandidateGenerator
from backend.fusion.entity_alignment import EntityAlignment
from backend.fusion.relation_alignment import RelationAlignment
from backend.fusion.utils.es_utils import ESUtils
from backend.fusion.utils.mysql_util import MysqlUtil
from backend.fusion.yky_knowledge_fusion import KnowledgeFusion as YKYKnowledgeFusion
from backend.graph.neo4j_utils import Neo4jUtils


@pytest.fixture(scope='session')
def yky_knowledge_fusion():
    conf_file = 'tests/fixtures/conf/config.yaml'
    return YKYKnowledgeFusion(ConfigUtils.load(conf_file, readonly=False))


@pytest.fixture(scope='session')
def mysql_util():
    return MysqlUtil()


@pytest.fixture(scope='session')
def relation_alignment():
    return RelationAlignment()


@pytest.fixture(scope='session')
def candidate_generator():
    conf_file = 'tests/fixtures/conf/config.yaml'
    return CandidateGenerator(ConfigUtils.load(conf_file))


@pytest.fixture(scope='session')
def neo4j_utils():
    conf_file = 'tests/fixtures/conf/config.yaml'
    return Neo4jUtils(ConfigUtils.load(conf_file))


@pytest.fixture(scope='session')
def entity_alignment():
    conf_file = 'tests/fixtures/conf/config.yaml'
    return EntityAlignment(ConfigUtils.load(conf_file))


@pytest.fixture(scope='session')
def es_utils():
    conf_file = 'tests/fixtures/conf/config.yaml'
    return ESUtils(ConfigUtils.load(conf_file))
