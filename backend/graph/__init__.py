from yunfu.common import ConfigUtils

from backend.models import GraphDbs

from .base_graph_mapper import BaseGraphMapper
from .nebula_graph_mapper import NebulaGraphMapper
from .nebula_utils import NebulaUtils
from .neo4j_graph_mapper import Neo4jGraphMapper
from .neo4j_utils import Neo4jUtils

config = ConfigUtils.load('conf/config.yaml')

__all__ = [
    "NebulaGraphMapper",
    "Neo4jGraphMapper",
    "BaseGraphMapper",
    "Neo4jUtils",
    "NebulaUtils",
    "get_graph_mapper",
]


def get_graph_mapper(db: GraphDbs, db_config: dict, enable_version: bool = True):
    db_config = config["neo4j"] if db == GraphDbs.NEO4J else config["nebula"]
    if db == GraphDbs.NEBULA:
        return NebulaGraphMapper(db_config, enable_version)
    if db == GraphDbs.NEO4J:
        return Neo4jGraphMapper(db_config, enable_version)
    raise ValueError(f"Unsupported db: {db}")
