from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union

from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb, Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node


class BaseGraphMapper(ABC):
    graph_db: Union[Neo4jGraphDb, NebulaGraphDb]

    @abstractmethod
    def get_node_by_eid(self, space: str, eid: str, label: str) -> Optional[Node]:
        """根据节点_eid获取节点

        :param space: 图空间名称
        :param eid: 节点_eid
        :param label: 节点标签
        :return: 节点
        """
        raise NotImplementedError

    @abstractmethod
    def get_nodes_by_label(self, space: str, label: str, skip: int, limit: int) -> List[Node]:
        """根据标签获取节点列表

        :param space: 图空间名称
        :param label: 节点标签
        :param skip: 跳过数量
        :param limit: 限制数量
        :return: 节点列表
        """
        raise NotImplementedError

    @abstractmethod
    def get_edges_by_head_eid(self, space: str, eid: str, label: str) -> List[Edge]:
        """根据head的_eid查关系

        :param space: 图空间名称
        :param eid: 头节点_eid
        :param label: 节点标签
        :return: 边列表
        """
        raise NotImplementedError

    @abstractmethod
    def create_entity(self, space: str, entity_data: Dict, label: str) -> Node:
        """创建实体节点

        :param space: 图空间名称
        :param entity_data: 实体数据
        :param label: 节点标签
        :return: 创建的节点
        """
        raise NotImplementedError

    @abstractmethod
    def update_entity_attributes(self, space: str, eid: str, attributes: Dict, label: str) -> Node:
        """更新实体属性

        :param space: 图空间名称
        :param eid: 实体_eid
        :param attributes: 属性字典
        :param label: 节点标签
        :return: 更新后的节点
        """
        raise NotImplementedError

    @abstractmethod
    def create_relation(self, space: str, head_eid: str, tail_eid: str, relation_name: str, label: str) -> Edge:
        """创建关系

        :param space: 图空间名称
        :param head_eid: 头节点_eid
        :param tail_eid: 尾节点_eid
        :param relation_name: 关系名称
        :param label: 节点标签
        :return: 创建的边
        """
        raise NotImplementedError

    @abstractmethod
    def has_relation_between_nodes(self, space: str, head_eid: str, tail_eid: str, relation_name: str, label: str) -> bool:
        """检查节点间是否存在关系

        :param space: 图空间名称
        :param head_eid: 头节点_eid
        :param tail_eid: 尾节点_eid
        :param relation_name: 关系名称
        :param label: 节点标签
        :return: 是否存在关系
        """
        raise NotImplementedError

    @abstractmethod
    def update_node_type(self, space: str, label: str) -> None:
        """更新节点类型

        :param space: 图空间名称
        :param label: 节点标签
        """
        raise NotImplementedError
