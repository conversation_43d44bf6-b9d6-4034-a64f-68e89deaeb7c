import time
from typing import Dict, List, Optional

from py2neo import Graph
from tenacity import retry, stop_after_attempt, wait_fixed
from yfflow import Yf<PERSON>ogger
from yunfu.common import ConfigUtils, yfid
from yunfu.db.graph.deprecated.models import Edge, Node
from yunfu.db.graph.deprecated.neo4j_client import Neo4jClient

from backend.fusion.schema import Entity, EntityTriples

logger = YfLogger(__name__)
conf = ConfigUtils.load('conf/config.yaml')


class Neo4jUtils(Neo4jClient):
    """
    图数据库工具类
    """
    def __init__(self, config: dict):
        self.graph_v1 = Graph(f'{config["neo4j"]["schema"]}://{config["neo4j"]["host"]}:{config["neo4j"]["port"]}/',
                              name=config['neo4j']['username'],
                              password=config['neo4j']['password'])
        super().__init__(config['neo4j']['host'], config['neo4j']['port'], config['neo4j']['username'],
                         config['neo4j']['password'])

    def get_edge_by_head_eid(self, _eid: str, label: str) -> List[Edge]:
        """根据head的_eid查关系"""
        query = f'''MATCH p = (n:`{label}`)-[r]->(m:`{label}`) WHERE n.`_eid`='{_eid}' RETURN p'''
        with self.graph.session() as session:
            result = session.run(query)
            paths = [record["p"] for record in result]
            return [self._parse_path(path) for path in paths]

    def get_node_by_eid(self, _eid: str, label: str) -> Node:
        """根据节点_eid获取节点

        :param uuid: 节点uuid
        :param *labels: 标签
        :return 节点
        :rtype: models.Node
        """
        query = f"MATCH (n:{label}) WHERE n.`_eid`='{_eid}' RETURN n limit 1"
        logger.info(f'get_node_by_eid query: {query}')
        result = self.run(query)
        node = self._parse_node(result[0]['n'])
        return node

    def get_node_by_label(self, label: str, skip: int, limit: int) -> Node:
        """根据标签获取节点

        :param uuid: 节点uuid
        :param *labels: 标签
        :return 节点
        :rtype: models.Node
        """
        query = f"MATCH (n:{label}) RETURN n skip {skip} limit {limit}"
        result = self.run(query)
        nodes = [self._parse_node(record['n']) for record in result]
        return nodes

    def get_entity_by_eid(self, eid: str, label: str) -> Optional[Node]:
        labels = [label]
        properties = {"_eid": eid}
        nodes = self.get_nodes(0, 1, *labels, **properties)
        return nodes[0] if nodes else None

    def update_attributes(self, entity: EntityTriples, label: str) -> Node:
        """更新属性,部分更新"""
        eid = entity.head.e_id
        node = self.get_node_by_eid(eid, label)
        attributes = entity.attributes
        properties = {}
        for attribute in attributes:
            properties[attribute.attribute_name] = attribute.attribute_value
        node.properties = properties
        node_: Node = self.partial_update_node(node=node)
        return node_

    def have_relation_between_node(self, path: Dict, label: str) -> bool:
        """根据head和tail的_eid以及关系名查关系路径"""
        head_eid = path['head_eid']
        relation_name = path['relation_name']
        tail_eid = path['tail_eid']
        query = (f'''MATCH p = (n:{label})-[r]->(m:{label})'''
                 f''' WHERE n._eid='{head_eid}' AND '''
                 f'''m._eid='{tail_eid}' AND '''
                 f'''r.name='{relation_name}' RETURN p''')
        with self.graph.session() as session:
            result = session.run(query)
            paths = [record["p"] for record in result]
        if paths:
            return True
        return False

    def create_relation(self, path: Dict[str, str], label: str) -> Edge:
        """创建关系"""
        head_eid = path['head_eid']
        relation_name = path['relation_name']
        tail_eid = path['tail_eid']
        src_node = self.get_node_by_eid(head_eid, label)
        dst_node = self.get_node_by_eid(tail_eid, label)
        relation_type = '属于' if relation_name == '属于' else '关联'
        rid = yfid(f'{relation_name}{time.time()}')[:6]
        properties = {
            'name': relation_name,
            'c': True,
            '_rid': rid,
            '_space': 'default'
        }
        edge = Edge(src_node, relation_type, dst_node, properties=properties)
        return edge
        # return self.create_edge(edge)

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    def create_entity(self, entity: EntityTriples, label: str) -> Node:
        """创建节点"""
        attributes = entity.attributes
        properties = {'name': entity.head.name, '_eid': entity.head.e_id, '_show_name': entity.head.name}
        for attribute in attributes:
            properties[attribute.attribute_name] = attribute.attribute_value
        node = Node(labels=[label], properties=properties)
        # node_: Node = self.create_node(node=node)
        return node

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    def create_entity_without_properties(self, entity: Entity, kg_label: str, fusion_type: str):
        labels = [kg_label, 'c', 'default']
        if fusion_type == 'ontology':
            labels.append('concept')
        properties = {'name': entity.name, '_eid': entity.e_id, '_show_name': entity.name}
        node = Node(labels=labels, properties=properties)
        return node

    def update_node_type(self, kg_label: str):
        query = f'''match (n:{kg_label})-[r]->(m:{kg_label}) where r.name='属于' set n._type=m.name'''
        with self.graph.session() as session:
            session.run(query)
